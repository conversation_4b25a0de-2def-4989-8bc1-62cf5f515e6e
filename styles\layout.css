/**
 * @file 布局样式文件
 * @description 容器布局、A4纸张、页眉页脚布局
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 主容器布局 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.form-section {
    background: white;
    border-radius: 8px;
    box-shadow: var(--box-shadow-medium);
    padding: 25px;
}

.preview-section {
    background: white;
    border-radius: 8px;
    box-shadow: var(--box-shadow-medium);
    padding: 25px;
}
/* #endregion */

/* #region A4纸张和文档容器样式 */
.a4-page {
    width: var(--a4-width-px);
    min-height: var(--a4-height-px);
    background: white;
    margin: 0 auto;
    box-shadow: var(--box-shadow-light);
    position: relative;
    overflow: hidden;
}

#document-preview {
    /* 严格A4尺寸设置 */
    width: var(--a4-width-px) !important;
    height: var(--a4-height-px) !important;
    min-width: var(--a4-width-px);
    min-height: var(--a4-height-px);
    max-width: var(--a4-width-px);
    max-height: var(--a4-height-px);
    margin: 0 auto 30px;
    padding: 0;
    background-color: white;
    box-shadow: var(--box-shadow-medium);
    transform: scale(var(--preview-scale-factor));
    transform-origin: top center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    /* 强制A4比例 - 210:297 = 1:1.414 */
    aspect-ratio: 210 / 297;
    display: block;
    flex-shrink: 0;
}

#document-container {
    position: relative;
    width: var(--a4-width-px);
    height: var(--a4-height-px);
    min-height: var(--a4-height-px);
    background: white;
    margin: 0;
    /* 统一边距设置 - 与导出模式保持一致，更新为30px */
    padding-top: 20px;
    padding-bottom: calc(var(--footer-height) + 15px);
    padding-left: var(--content-margin-left);  /* 统一为30px */
    padding-right: var(--content-margin-right); /* 统一为30px */
    display: flex;
    flex-direction: column;
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--text-color);
    overflow: hidden;
    transform-origin: top center;
    box-sizing: border-box;
}

#preview-container {
    background: #f5f5f5;
    padding: 20px;
    /* 精确计算容器尺寸以适应缩放后的A4 */
    min-height: calc(var(--a4-height-px) * var(--preview-scale-factor) + 80px);
    min-width: calc(var(--a4-width-px) * var(--preview-scale-factor) + 60px);
    width: 100%;
    max-width: 100%;
    overflow: auto;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

/* A4预览容器增强 */
#preview-container::before {
    content: "A4 预览 (794×1123px) / A4 Preview";
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 11px;
    color: #666;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 8px;
    border-radius: 3px;
    z-index: 1000;
}

/* A4预览精确度验证 */
#document-preview::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px dashed rgba(30, 64, 175, 0.3);
    pointer-events: none;
    z-index: 999;
}

/* 内容溢出警告 */
.content-overflow-warning {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(239, 68, 68, 0.9);
    color: rgba(255, 255, 255, 0);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1001;
    display: none;
}
/* #endregion */

/* #region 页眉页脚布局 */
.document-header,
.document-header-image-container {
    position: relative;
    width: 100%;
    height: var(--header-height);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    padding: 5px;
    box-sizing: border-box;
    margin: 0 auto 15px auto;
    overflow: hidden; /* 防止图片超出容器 */
}

.document-footer,
.document-footer-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--footer-height);
    z-index: var(--z-index-footer);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid var(--border-color);
    box-sizing: border-box;
    max-width: var(--a4-width-px);
    margin: 0 auto;
}

.document-footer-content {
    width: 100%;
    text-align: center;
    font-size: 8pt;
    color: #666;
    line-height: 1.2;
    padding: 2px 5px;
}

/* 统一页脚样式 - 确保固定在A4页面底部边缘 */
.unified-document-footer,
.company-footer-image-container {
    position: absolute !important;
    bottom: 0 !important; /* 固定在A4页面底部边缘 */
    left: 0 !important;
    right: 0 !important;
    height: var(--footer-height) !important; /* 固定高度110px */
    background-color: white !important;
    z-index: var(--z-index-footer) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 5px !important;
    box-sizing: border-box !important;
    width: 100% !important;
    margin: 0 !important;
}

.unified-document-footer img {
    height: var(--footer-height);
    width: auto;
    max-width: 100%;
    object-fit: var(--image-object-fit);
    object-position: var(--image-object-position);
    margin: 0 auto;
    display: block;
}
/* #endregion */

/* #region 印章定位 */
.header-placeholder {
    height: var(--header-height);
    margin-bottom: 15px;
    position: relative;
    width: 100%;
}

.footer-placeholder {
    height: var(--footer-height);
    margin-top: 10px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--z-index-footer);
}

.stamp-placeholder {
    width: 96px;
    height: 96px;
    position: absolute;
    bottom: var(--stamp-bottom-offset);
    right: var(--stamp-right-offset);
    z-index: var(--z-index-stamp);
    border-radius: 50%;
}

/* 印章容器定位 */
.company-stamp {
    position: absolute;
    bottom: var(--stamp-bottom-offset);
    right: var(--stamp-right-offset);
    z-index: var(--z-index-stamp);
    width: 96px;
    height: 96px;
    box-sizing: border-box;
}
/* #endregion */

/* #region 电子生成标识 */
.electronic-signature {
    position: absolute;
    bottom: calc(var(--footer-height) + 20px);
    left: 0;
    right: 0;
    text-align: center;
    font-size: 12px;
    color: #999;
    z-index: 5;
    font-style: italic;
}
/* #endregion */

/* #region 内容适配和响应式字体 */
.content-adaptive {
    font-size: clamp(10px, 2vw, 12px);
    line-height: 1.4;
}

.content-adaptive h1 {
    font-size: clamp(16px, 3vw, 20px);
    margin-bottom: 10px;
}

.content-adaptive h2 {
    font-size: clamp(14px, 2.5vw, 16px);
    margin-bottom: 8px;
}

.items-table.adaptive {
    font-size: clamp(9px, 1.8vw, 11px);
}

.items-table.adaptive th,
.items-table.adaptive td {
    padding: clamp(4px, 1vw, 8px);
}
/* #endregion */
