/**
 * @file 组件样式文件
 * @description 表单、按钮、表格、AI填充等组件样式
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 标题样式 */
.main-title {
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 600;
}

.section-title {
    margin-bottom: 20px;
    color: var(--dark-color);
    font-size: 20px;
    font-weight: 600;
}
/* #endregion */

/* #region 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}
/* #endregion */

/* #region 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    display: inline-block;
    text-decoration: none;
    text-align: center;
    line-height: 1;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1e3a8a;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #2563eb;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.export-btn {
    min-width: 120px;
}
/* #endregion */

/* #region 表格样式 */
.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    /* 应用内容边距 - 修复文字紧贴边缘问题 */
    margin-left: var(--content-margin-left);
    margin-right: var(--content-margin-right);
    width: calc(100% - var(--content-margin-left) - var(--content-margin-right));
}

.items-table th,
.items-table td {
    padding: 8px;
    border: 1px solid var(--border-color);
    text-align: left;
}

.items-table th {
    background-color: var(--light-color);
    font-weight: 600;
}

.items-table input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 4px;
    font-size: 14px;
}

.items-table input:focus {
    outline: 1px solid var(--primary-color);
    background: white;
}

.item-amount {
    text-align: right;
    font-weight: 600;
}

.total-amount-display {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
    text-align: center;
    padding: 15px;
    background: var(--light-color);
    border-radius: 6px;
    margin-top: 10px;
}

.total-amount-container {
    background-color: #ffffff;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 18px;
    margin: 15px 0;
    border-radius: 6px;
    box-shadow: none;
    z-index: var(--z-index-total);
    position: relative;
    display: inline-block;
    min-width: 200px;
    text-align: center;
    /* 移除额外边距 - 容器已统一设置10px边距 */
    margin-left: 0;
    margin-right: 0;
}

.total-amount-container h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
    line-height: 1.4;
    color: var(--primary-color);
    text-shadow: none;
    white-space: nowrap;
}
/* #endregion */

/* #region 内容区域样式 - 统一边距处理 */
.company-info,
.customer-info,
.payment-info,
.document-title,
.notes-section {
    /* 移除额外的内容边距 - 容器已统一设置10px边距 */
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
}

/* 特殊处理：备注区域保持简洁 */
.notes-section {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
}

.company-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.customer-info,
.payment-info {
    margin-bottom: 15px;
}

.document-title {
    text-align: center;
    font-size: var(--title-font-size);
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 20px;
}
/* #endregion */

/* #region AI填充组件样式 - 简化版 */
.ai-fill-container {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 20px;
    background: #f9fafb;
}

.ai-fill-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.ai-fill-title {
    margin: 0;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
}

.ai-form-row {
    margin-bottom: 12px;
}

.ai-image-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
}

.ai-status {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
}

.ai-status.success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.ai-status.error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.ai-status.info {
    background-color: #dbeafe;
    color: var(--primary-color);
    border: 1px solid #93c5fd;
}

.ai-image-hint {
    color: #666;
    font-size: 12px;
}
/* #endregion */

/* #region 多订单管理器样式 */
.multi-order-container {
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.order-manager {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.order-tabs {
    display: flex;
    gap: 5px;
}

.order-tab {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.order-tab:hover {
    background: #e9ecef;
}

.order-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.order-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.current-order-info {
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 14px;
}

.current-order-info span {
    margin-right: 15px;
}

.order-badge {
    display: inline-block;
    padding: 2px 8px;
    background: var(--primary-color);
    color: white;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.order-column {
    width: 100px;
    text-align: center;
}

.order-info {
    font-size: 12px;
    color: #666;
    text-align: center;
}

/* 多订单隐藏类 */
.multi-order-hidden {
    display: none;
}

.order-column-hidden {
    display: none;
}

/* 合并视图样式 */
.combined-view .item-description input,
.combined-view .item-quantity input,
.combined-view .item-price input {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    cursor: default;
}

.combined-view .item-description input:focus,
.combined-view .item-quantity input:focus,
.combined-view .item-price input:focus {
    outline: none;
    box-shadow: none;
}
/* #endregion */

/* #region 预览控制样式 */
.preview-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.export-method-selector {
    margin-right: 10px;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.export-method-info {
    margin-top: 8px;
    padding: 6px 10px;
    background: #f0f9ff;
    border-radius: 4px;
    font-size: 12px;
    color: var(--primary-color);
}

.preview-status-indicator {
    font-size: 12px;
    color: #666;
}

.empty-preview-message {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
}
/* #endregion */
