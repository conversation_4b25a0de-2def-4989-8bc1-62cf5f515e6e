{"models": [{"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash", "description": "快速响应，适合实时应用", "category": "production", "costLevel": "low", "speedLevel": "high", "accuracyLevel": "medium"}, {"id": "gemini-1.5-flash-8b", "name": "Gemini 1.5 Flash 8B", "description": "轻量级模型，成本更低", "category": "production", "costLevel": "very-low", "speedLevel": "very-high", "accuracyLevel": "medium"}, {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "description": "专业版本，准确率更高", "category": "production", "costLevel": "medium", "speedLevel": "medium", "accuracyLevel": "high"}, {"id": "gemini-2.0-flash-exp", "name": "Gemini 2.0 Flash", "description": "实验版本，最新功能", "category": "experimental", "costLevel": "medium", "speedLevel": "high", "accuracyLevel": "high"}, {"id": "gemini-exp-1206", "name": "Gemini Exp 1206", "description": "实验版本，特殊优化", "category": "experimental", "costLevel": "medium", "speedLevel": "medium", "accuracyLevel": "high"}, {"id": "gemini-2.0-flash-thinking-exp", "name": "Gemini 2.0 Flash Thinking", "description": "思维链推理版本", "category": "experimental", "costLevel": "high", "speedLevel": "low", "accuracyLevel": "very-high"}], "scenarios": [{"id": "order-parsing", "name": "订单信息解析", "description": "测试AI解析结构化订单信息的能力", "category": "basic", "difficulty": "easy", "prompt": "请解析以下订单信息并以JSON格式返回：\n订单号：ORD-2024-001\n客户：张三公司\n项目：网站开发服务 x2 单价：5000元\n项目：移动端开发 x1 单价：8000元\n总金额：18000元\n请返回包含订单号、客户名称、项目列表、总金额的JSON数据。", "expectedFields": ["orderNumber", "customer", "items", "totalAmount"], "weight": 1.0}, {"id": "multi-format", "name": "多文档格式识别", "description": "测试识别和处理不同文档格式的能力", "category": "advanced", "difficulty": "medium", "prompt": "识别以下文档类型并提取关键信息：\n发票抬头：北京科技有限公司\n发票号码：INV-2024-0156\n开票日期：2024-01-15\n商品明细：\n- 软件开发服务 数量:1 单价:15000.00 金额:15000.00\n- 技术支持服务 数量:12 单价:800.00 金额:9600.00\n税率：13%\n合计金额：24600.00\n请识别文档类型并返回结构化数据。", "expectedFields": ["documentType", "invoiceNumber", "company", "items", "taxRate", "totalAmount"], "weight": 1.2}, {"id": "ocr-extraction", "name": "图片OCR文字提取", "description": "测试处理OCR错误和文字纠正的能力", "category": "advanced", "difficulty": "hard", "prompt": "模拟OCR场景：从以下模糊文本中提取准确信息：\n收据编号：RCP-2O24-OO89 (注意：包含字母O和数字0的混淆)\n日期：2O24/O1/2O (注意：包含字母O和数字0的混淆)\n金额：￥1,5OO.OO (注意：包含字母O和数字0的混淆)\n请纠正OCR错误并返回准确的结构化数据。", "expectedFields": ["receiptNumber", "date", "amount"], "weight": 1.5}, {"id": "data-validation", "name": "数据验证与纠错", "description": "测试数据验证和错误修正的能力", "category": "expert", "difficulty": "hard", "prompt": "验证并纠正以下数据中的错误：\n订单信息：\n- 订单号：ORD-2024-001\n- 日期：2024-13-45 (错误日期)\n- 金额：-500.00 (负数金额)\n- 数量：0.5个 (小数数量但单位不支持)\n- 税率：150% (超出合理范围)\n请识别错误并提供合理的纠正建议。", "expectedFields": ["errors", "corrections", "validatedData"], "weight": 1.8}, {"id": "batch-processing", "name": "批量数据处理", "description": "测试处理多个数据项的效率和准确性", "category": "expert", "difficulty": "medium", "prompt": "批量处理以下3个订单数据：\n订单1：客户A，商品X x2，单价100元\n订单2：客户B，商品Y x1，单价200元，商品Z x3，单价50元\n订单3：客户C，商品X x1，单价100元，商品W x2，单价75元\n请计算每个订单的总金额，并生成汇总报告。", "expectedFields": ["orders", "summary", "totalRevenue"], "weight": 1.3}], "testSettings": {"defaultRounds": 3, "maxRounds": 10, "requestDelay": 1000, "timeout": 30000, "retryAttempts": 2}, "evaluationCriteria": {"responseTime": {"excellent": 2000, "good": 5000, "acceptable": 10000}, "accuracy": {"excellent": 90, "good": 80, "acceptable": 70}, "stability": {"excellent": 95, "good": 90, "acceptable": 85}}, "scoring": {"weights": {"speed": 0.3, "accuracy": 0.4, "stability": 0.2, "cost": 0.1}, "penalties": {"timeout": -20, "error": -10, "invalidResponse": -15}}, "export": {"formats": ["json", "csv", "pdf"], "defaultFields": ["model", "scenario", "responseTime", "accuracy", "success", "timestamp"], "optionalFields": ["response", "error", "round", "retryCount"]}, "ui": {"theme": {"primaryColor": "#3b82f6", "successColor": "#10b981", "warningColor": "#f59e0b", "errorColor": "#ef4444"}, "charts": {"defaultType": "bar", "colors": ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"]}}, "api": {"baseUrl": "https://generativelanguage.googleapis.com/v1beta", "defaultConfig": {"temperature": 0.1, "topK": 1, "topP": 1, "maxOutputTokens": 2048}, "rateLimit": {"requestsPerMinute": 60, "requestsPerDay": 1500}}, "storage": {"maxHistoryItems": 20, "autoSave": true, "compressionEnabled": false}, "version": "1.0.0", "lastUpdated": "2024-01-20"}