{"models": [{"id": "gemini-2.5-pro", "name": "Gemini 2.5 Pro", "description": "最强大的稳定版模型，具备自适应思维能力", "category": "latest-stable", "status": "stable", "releaseDate": "2025-06-17", "costLevel": "high", "speedLevel": "medium", "accuracyLevel": "very-high", "contextWindow": "2M tokens", "outputTokens": "8K tokens", "features": ["思维模式", "多模态", "函数调用", "代码执行", "自适应思维"]}, {"id": "gemini-2.5-flash", "name": "Gemini 2.5 Flash", "description": "首个稳定的2.5 Flash模型，性价比最佳", "category": "latest-stable", "status": "stable", "releaseDate": "2025-06-17", "costLevel": "medium", "speedLevel": "high", "accuracyLevel": "high", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["快速响应", "多模态", "思维能力", "价格优化"]}, {"id": "gemini-2.0-flash", "name": "Gemini 2.0 Flash", "description": "下一代功能，比1.5 Pro快2倍", "category": "production", "status": "stable", "releaseDate": "2025-02-05", "costLevel": "medium", "speedLevel": "very-high", "accuracyLevel": "high", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["多模态生成", "工具使用", "函数调用", "双向流式"]}, {"id": "gemini-2.0-flash-lite", "name": "Gemini 2.0 Flash Lite", "description": "成本优化版本，速度和规模优化", "category": "production", "status": "stable", "releaseDate": "2025-02-25", "costLevel": "low", "speedLevel": "very-high", "accuracyLevel": "medium", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["成本效益", "高速处理", "规模优化"]}, {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "description": "复杂推理和分析的专业版本", "category": "production", "status": "stable", "releaseDate": "2024-05-23", "costLevel": "medium", "speedLevel": "medium", "accuracyLevel": "high", "contextWindow": "2M tokens", "outputTokens": "8K tokens", "features": ["长上下文", "复杂推理", "多模态", "函数调用"]}, {"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash", "description": "快速多用途模型，平衡性能和成本", "category": "production", "status": "stable", "releaseDate": "2024-05-23", "costLevel": "low", "speedLevel": "high", "accuracyLevel": "medium", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["快速响应", "多模态", "函数调用", "成本效益"]}, {"id": "gemini-1.5-flash-8b", "name": "Gemini 1.5 Flash 8B", "description": "最小模型，适合高频调用", "category": "production", "status": "stable", "releaseDate": "2024-10-03", "costLevel": "very-low", "speedLevel": "very-high", "accuracyLevel": "medium", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["超低成本", "高频优化", "轻量级"]}, {"id": "gemini-2.5-flash-lite-preview-06-17", "name": "Gemini 2.5 Flash Lite Preview", "description": "低成本高性能的2.5模型预览版", "category": "preview", "status": "preview", "releaseDate": "2025-06-17", "costLevel": "very-low", "speedLevel": "very-high", "accuracyLevel": "medium", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["成本优化", "预览功能", "高性能"]}, {"id": "gemini-2.0-flash-preview-image-generation", "name": "Gemini 2.0 Flash 图像生成", "description": "图像生成和编辑预览模型", "category": "preview", "status": "preview", "releaseDate": "2025-05-07", "costLevel": "medium", "speedLevel": "medium", "accuracyLevel": "high", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["图像生成", "图像编辑", "多模态输出"]}, {"id": "gemini-2.0-flash-live-001", "name": "Gemini 2.0 Flash Live", "description": "实时交互和双向流式处理", "category": "specialized", "status": "stable", "releaseDate": "2025-04-09", "costLevel": "medium", "speedLevel": "real-time", "accuracyLevel": "high", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["实时交互", "语音输出", "流式处理", "会话管理"]}, {"id": "text-embedding-004", "name": "Text Embedding 004", "description": "文本嵌入模型，支持弹性嵌入大小", "category": "specialized", "status": "stable", "releaseDate": "2024-04-09", "costLevel": "low", "speedLevel": "high", "accuracyLevel": "high", "contextWindow": "N/A", "outputTokens": "N/A", "features": ["文本嵌入", "弹性大小", "语义检索"]}, {"id": "gemini-2.0-flash-001", "name": "Gemini 2.0 Flash 001", "description": "2.0 Flash的固定版本", "category": "versioned", "status": "stable", "releaseDate": "2025-02-05", "costLevel": "medium", "speedLevel": "very-high", "accuracyLevel": "high", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["版本固定", "文本输出", "一致性保证"]}, {"id": "gemini-1.5-flash-002", "name": "Gemini 1.5 Flash 002", "description": "1.5 Flash的最新固定版本", "category": "versioned", "status": "stable", "releaseDate": "2024-09-24", "costLevel": "low", "speedLevel": "high", "accuracyLevel": "medium", "contextWindow": "1M tokens", "outputTokens": "8K tokens", "features": ["版本固定", "稳定性保证", "频率惩罚"]}, {"id": "gemini-1.5-pro-002", "name": "Gemini 1.5 Pro 002", "description": "1.5 Pro的最新固定版本", "category": "versioned", "status": "stable", "releaseDate": "2024-09-24", "costLevel": "medium", "speedLevel": "medium", "accuracyLevel": "high", "contextWindow": "2M tokens", "outputTokens": "8K tokens", "features": ["版本固定", "复杂推理", "频率惩罚"]}], "scenarios": [{"id": "order-parsing", "name": "订单信息解析", "description": "测试AI解析结构化订单信息的能力", "category": "basic", "difficulty": "easy", "prompt": "请解析以下订单信息并以JSON格式返回：\n订单号：ORD-2024-001\n客户：张三公司\n项目：网站开发服务 x2 单价：5000元\n项目：移动端开发 x1 单价：8000元\n总金额：18000元\n请返回包含订单号、客户名称、项目列表、总金额的JSON数据。", "expectedFields": ["orderNumber", "customer", "items", "totalAmount"], "weight": 1.0}, {"id": "multi-format", "name": "多文档格式识别", "description": "测试识别和处理不同文档格式的能力", "category": "advanced", "difficulty": "medium", "prompt": "识别以下文档类型并提取关键信息：\n发票抬头：北京科技有限公司\n发票号码：INV-2024-0156\n开票日期：2024-01-15\n商品明细：\n- 软件开发服务 数量:1 单价:15000.00 金额:15000.00\n- 技术支持服务 数量:12 单价:800.00 金额:9600.00\n税率：13%\n合计金额：24600.00\n请识别文档类型并返回结构化数据。", "expectedFields": ["documentType", "invoiceNumber", "company", "items", "taxRate", "totalAmount"], "weight": 1.2}, {"id": "ocr-extraction", "name": "图片OCR文字提取", "description": "测试处理OCR错误和文字纠正的能力", "category": "advanced", "difficulty": "hard", "prompt": "模拟OCR场景：从以下模糊文本中提取准确信息：\n收据编号：RCP-2O24-OO89 (注意：包含字母O和数字0的混淆)\n日期：2O24/O1/2O (注意：包含字母O和数字0的混淆)\n金额：￥1,5OO.OO (注意：包含字母O和数字0的混淆)\n请纠正OCR错误并返回准确的结构化数据。", "expectedFields": ["receiptNumber", "date", "amount"], "weight": 1.5}, {"id": "data-validation", "name": "数据验证与纠错", "description": "测试数据验证和错误修正的能力", "category": "expert", "difficulty": "hard", "prompt": "验证并纠正以下数据中的错误：\n订单信息：\n- 订单号：ORD-2024-001\n- 日期：2024-13-45 (错误日期)\n- 金额：-500.00 (负数金额)\n- 数量：0.5个 (小数数量但单位不支持)\n- 税率：150% (超出合理范围)\n请识别错误并提供合理的纠正建议。", "expectedFields": ["errors", "corrections", "validatedData"], "weight": 1.8}, {"id": "batch-processing", "name": "批量数据处理", "description": "测试处理多个数据项的效率和准确性", "category": "expert", "difficulty": "medium", "prompt": "批量处理以下3个订单数据：\n订单1：客户A，商品X x2，单价100元\n订单2：客户B，商品Y x1，单价200元，商品Z x3，单价50元\n订单3：客户C，商品X x1，单价100元，商品W x2，单价75元\n请计算每个订单的总金额，并生成汇总报告。", "expectedFields": ["orders", "summary", "totalRevenue"], "weight": 1.3}], "testSettings": {"defaultRounds": 3, "maxRounds": 10, "requestDelay": 1000, "timeout": 30000, "retryAttempts": 2}, "evaluationCriteria": {"responseTime": {"excellent": 2000, "good": 5000, "acceptable": 10000}, "accuracy": {"excellent": 90, "good": 80, "acceptable": 70}, "stability": {"excellent": 95, "good": 90, "acceptable": 85}}, "scoring": {"weights": {"speed": 0.3, "accuracy": 0.4, "stability": 0.2, "cost": 0.1}, "penalties": {"timeout": -20, "error": -10, "invalidResponse": -15}}, "export": {"formats": ["json", "csv", "pdf"], "defaultFields": ["model", "scenario", "responseTime", "accuracy", "success", "timestamp"], "optionalFields": ["response", "error", "round", "retryCount"]}, "ui": {"theme": {"primaryColor": "#3b82f6", "successColor": "#10b981", "warningColor": "#f59e0b", "errorColor": "#ef4444"}, "charts": {"defaultType": "bar", "colors": ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"]}}, "api": {"baseUrl": "https://generativelanguage.googleapis.com/v1beta", "defaultConfig": {"temperature": 0.1, "topK": 1, "topP": 1, "maxOutputTokens": 2048}, "rateLimit": {"requestsPerMinute": 60, "requestsPerDay": 1500}}, "storage": {"maxHistoryItems": 20, "autoSave": true, "compressionEnabled": false}, "version": "1.0.0", "lastUpdated": "2024-01-20"}