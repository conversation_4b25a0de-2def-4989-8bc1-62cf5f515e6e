<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini AI 模型性能测试工具 - SmartOffice</title>

    <!-- 引入项目CSS样式 -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/layout.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/responsive.css">

    <!-- Chart.js for 数据可视化 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        /* 测试工具专用样式 */
        .tester-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .tester-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .tester-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3b82f6;
        }

        .model-selector {
            margin-bottom: 20px;
        }

        .model-selector label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .model-selector select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .test-scenario {
            margin-bottom: 20px;
        }

        .scenario-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .scenario-item:hover {
            background-color: #f9fafb;
            border-color: #3b82f6;
        }

        .scenario-item.selected {
            background-color: #dbeafe;
            border-color: #3b82f6;
        }

        .scenario-item input[type="checkbox"] {
            margin-right: 10px;
        }

        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .test-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .test-btn.primary:hover {
            background: #2563eb;
        }

        .test-btn.secondary {
            background: #6b7280;
            color: white;
        }

        .test-btn.secondary:hover {
            background: #4b5563;
        }

        .test-btn:disabled {
            background: #d1d5db;
            color: #9ca3af;
            cursor: not-allowed;
        }

        .results-panel {
            grid-column: 1 / -1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .results-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .results-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .results-tab.active {
            border-bottom-color: #3b82f6;
            color: #3b82f6;
            font-weight: 600;
        }

        .results-content {
            display: none;
        }

        .results-content.active {
            display: block;
        }

        .metric-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .metric-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
        }

        .metric-unit {
            font-size: 14px;
            color: #64748b;
            margin-left: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            transition: width 0.3s ease;
            width: 0%;
        }

        .test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-idle { background: #6b7280; }
        .status-running { background: #f59e0b; animation: pulse 1s infinite; }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .tester-grid {
                grid-template-columns: 1fr;
            }

            .test-controls {
                flex-direction: column;
            }

            .results-tabs {
                flex-wrap: wrap;
            }

            .results-tab {
                flex: 1;
                text-align: center;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="tester-container">
        <!-- 页面标题 -->
        <div class="tester-header">
            <h1>🤖 Gemini AI 模型性能测试工具</h1>
            <p>专为发票/收据生成器项目优化的AI模型性能对比测试平台</p>
        </div>

        <!-- 主要测试区域 -->
        <div class="tester-grid">
            <!-- 左侧：测试配置面板 -->
            <div class="test-panel">
                <h2 class="panel-title">🔧 测试配置</h2>

                <!-- 模型选择器 -->
                <div class="model-selector">
                    <label for="model-select">选择测试模型</label>
                    <select id="model-select" multiple size="10">
                        <!-- 最新稳定版模型 (2025年6月发布) -->
                        <optgroup label="🌟 最新稳定版 (Latest Stable - June 2025)">
                            <option value="gemini-2.5-pro" selected>Gemini 2.5 Pro (最强思维模型)</option>
                            <option value="gemini-2.5-flash">Gemini 2.5 Flash (性价比最佳)</option>
                        </optgroup>

                        <!-- 稳定版模型 -->
                        <optgroup label="🟢 稳定版 (Production Ready)">
                            <option value="gemini-2.0-flash">Gemini 2.0 Flash (下一代功能)</option>
                            <option value="gemini-2.0-flash-lite">Gemini 2.0 Flash Lite (成本优化)</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro (复杂推理)</option>
                            <option value="gemini-1.5-flash">Gemini 1.5 Flash (快速多用途)</option>
                            <option value="gemini-1.5-flash-8b">Gemini 1.5 Flash 8B (高频调用)</option>
                        </optgroup>

                        <!-- 预览版模型 -->
                        <optgroup label="🟡 预览版 (Preview)">
                            <option value="gemini-2.5-flash-lite-preview-06-17">Gemini 2.5 Flash Lite (低成本预览)</option>
                            <option value="gemini-2.0-flash-preview-image-generation">Gemini 2.0 Flash 图像生成</option>
                        </optgroup>

                        <!-- 特殊功能模型 -->
                        <optgroup label="🎯 特殊功能 (Specialized)">
                            <option value="gemini-2.0-flash-live-001">Gemini 2.0 Flash Live (实时交互)</option>
                            <option value="text-embedding-004">Text Embedding 004 (文本嵌入)</option>
                        </optgroup>

                        <!-- 固定版本 -->
                        <optgroup label="📌 固定版本 (Specific Versions)">
                            <option value="gemini-2.0-flash-001">Gemini 2.0 Flash 001</option>
                            <option value="gemini-1.5-flash-002">Gemini 1.5 Flash 002</option>
                            <option value="gemini-1.5-pro-002">Gemini 1.5 Pro 002</option>
                        </optgroup>
                    </select>
                    <small style="color: #6b7280; margin-top: 5px; display: block;">
                        按住 Ctrl/Cmd 键可选择多个模型进行对比测试<br>
                        <strong>推荐：</strong>稳定版用于生产环境，实验版用于功能测试
                    </small>

                    <!-- 模型信息显示 -->
                    <div id="model-info" style="margin-top: 10px; padding: 10px; background: #f8fafc; border-radius: 6px; border: 1px solid #e2e8f0; display: none;">
                        <div style="font-size: 13px; font-weight: 600; color: #374151; margin-bottom: 5px;">模型信息</div>
                        <div id="model-details" style="font-size: 12px; color: #6b7280;"></div>
                    </div>
                </div>

                <!-- API Key 配置 -->
                <div class="form-group">
                    <label for="api-key">Gemini API Key</label>
                    <input type="password" id="api-key" placeholder="输入您的 Gemini API Key"
                           style="width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px;">
                    <small style="color: #6b7280; margin-top: 5px; display: block;">
                        API Key 仅在本地存储，不会上传到服务器
                    </small>
                </div>

                <!-- 测试场景选择 -->
                <div class="test-scenario">
                    <label>测试场景</label>
                    <div class="scenario-item" data-scenario="order-parsing">
                        <input type="checkbox" id="scenario-1" checked>
                        <label for="scenario-1">📋 订单信息解析</label>
                    </div>
                    <div class="scenario-item" data-scenario="multi-format">
                        <input type="checkbox" id="scenario-2" checked>
                        <label for="scenario-2">📄 多文档格式识别</label>
                    </div>
                    <div class="scenario-item" data-scenario="ocr-extraction">
                        <input type="checkbox" id="scenario-3">
                        <label for="scenario-3">🖼️ 图片OCR文字提取</label>
                    </div>
                    <div class="scenario-item" data-scenario="data-validation">
                        <input type="checkbox" id="scenario-4">
                        <label for="scenario-4">✅ 数据验证与纠错</label>
                    </div>
                    <div class="scenario-item" data-scenario="batch-processing">
                        <input type="checkbox" id="scenario-5">
                        <label for="scenario-5">🔄 批量数据处理</label>
                    </div>
                </div>

                <!-- 测试参数 -->
                <div class="form-group">
                    <label for="test-rounds">测试轮数</label>
                    <input type="number" id="test-rounds" value="3" min="1" max="10"
                           style="width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px;">
                </div>

                <!-- 测试控制按钮 -->
                <div class="test-controls">
                    <button class="test-btn primary" id="start-test" onclick="startTest()">
                        <span class="status-indicator status-idle" id="test-status"></span>
                        开始测试
                    </button>
                    <button class="test-btn secondary" id="stop-test" onclick="stopTest()" disabled>
                        停止测试
                    </button>
                </div>

                <!-- 测试进度 -->
                <div class="progress-container" style="display: none;" id="progress-container">
                    <div class="metric-title">测试进度</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 12px; color: #6b7280;" id="progress-text">
                        准备开始测试...
                    </div>
                </div>
            </div>

            <!-- 右侧：实时监控面板 -->
            <div class="test-panel">
                <h2 class="panel-title">📊 实时监控</h2>

                <!-- 当前测试状态 -->
                <div class="metric-card">
                    <div class="metric-title">当前测试模型</div>
                    <div class="metric-value" id="current-model">未开始</div>
                </div>

                <div class="metric-card">
                    <div class="metric-title">平均响应时间</div>
                    <div class="metric-value" id="avg-response-time">0<span class="metric-unit">ms</span></div>
                </div>

                <div class="metric-card">
                    <div class="metric-title">成功率</div>
                    <div class="metric-value" id="success-rate">0<span class="metric-unit">%</span></div>
                </div>

                <div class="metric-card">
                    <div class="metric-title">已完成测试</div>
                    <div class="metric-value" id="completed-tests">0<span class="metric-unit">次</span></div>
                </div>

                <!-- 实时日志 -->
                <div style="margin-top: 20px;">
                    <div class="metric-title">实时日志</div>
                    <div class="test-log" id="test-log">等待测试开始...</div>
                </div>
            </div>
        </div>

        <!-- 测试结果面板 -->
        <div class="results-panel">
            <h2 class="panel-title">📈 测试结果分析</h2>

            <!-- 结果标签页 -->
            <div class="results-tabs">
                <div class="results-tab active" onclick="switchTab('overview')">概览</div>
                <div class="results-tab" onclick="switchTab('performance')">性能对比</div>
                <div class="results-tab" onclick="switchTab('accuracy')">准确率分析</div>
                <div class="results-tab" onclick="switchTab('history')">历史记录</div>
                <div class="results-tab" onclick="switchTab('export')">数据导出</div>
            </div>

            <!-- 概览标签页 -->
            <div class="results-content active" id="overview-content">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div class="metric-card">
                        <div class="metric-title">最快模型</div>
                        <div class="metric-value" id="fastest-model">-</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">最准确模型</div>
                        <div class="metric-value" id="most-accurate-model">-</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">推荐模型</div>
                        <div class="metric-value" id="recommended-model">-</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">总测试时间</div>
                        <div class="metric-value" id="total-test-time">0<span class="metric-unit">秒</span></div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="overview-chart"></canvas>
                </div>
            </div>

            <!-- 性能对比标签页 -->
            <div class="results-content" id="performance-content">
                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>

                <div id="performance-table-container">
                    <table class="items-table" id="performance-table">
                        <thead>
                            <tr>
                                <th>模型名称</th>
                                <th>平均响应时间</th>
                                <th>最快响应</th>
                                <th>最慢响应</th>
                                <th>稳定性评分</th>
                            </tr>
                        </thead>
                        <tbody id="performance-table-body">
                            <tr>
                                <td colspan="5" style="text-align: center; color: #6b7280; padding: 20px;">
                                    暂无测试数据，请先运行测试
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 准确率分析标签页 -->
            <div class="results-content" id="accuracy-content">
                <div class="chart-container">
                    <canvas id="accuracy-chart"></canvas>
                </div>

                <div id="accuracy-details">
                    <h3 style="margin-bottom: 15px;">场景准确率详情</h3>
                    <div id="scenario-accuracy-list">
                        <p style="text-align: center; color: #6b7280; padding: 20px;">
                            暂无准确率数据，请先运行测试
                        </p>
                    </div>
                </div>
            </div>

            <!-- 历史记录标签页 -->
            <div class="results-content" id="history-content">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 15px;">
                    <h3>测试历史记录</h3>
                    <button class="test-btn secondary" onclick="clearHistory()" style="padding: 8px 16px; font-size: 12px;">
                        清空历史
                    </button>
                </div>

                <div id="history-list">
                    <p style="text-align: center; color: #6b7280; padding: 20px;">
                        暂无历史记录
                    </p>
                </div>
            </div>

            <!-- 数据导出标签页 -->
            <div class="results-content" id="export-content">
                <div style="margin-bottom: 20px;">
                    <h3>数据导出选项</h3>
                    <p style="color: #6b7280; margin-bottom: 15px;">
                        选择要导出的数据格式和内容
                    </p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div class="scenario-item">
                        <input type="checkbox" id="export-performance" checked>
                        <label for="export-performance">性能数据</label>
                    </div>
                    <div class="scenario-item">
                        <input type="checkbox" id="export-accuracy" checked>
                        <label for="export-accuracy">准确率数据</label>
                    </div>
                    <div class="scenario-item">
                        <input type="checkbox" id="export-logs">
                        <label for="export-logs">测试日志</label>
                    </div>
                    <div class="scenario-item">
                        <input type="checkbox" id="export-charts">
                        <label for="export-charts">图表数据</label>
                    </div>
                </div>

                <div class="test-controls">
                    <button class="test-btn primary" onclick="exportData('json')">导出 JSON</button>
                    <button class="test-btn primary" onclick="exportData('csv')">导出 CSV</button>
                    <button class="test-btn secondary" onclick="exportData('pdf')">导出 PDF 报告</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let testResults = [];
        let currentTest = null;
        let testHistory = JSON.parse(localStorage.getItem('gemini-test-history') || '[]');
        let charts = {};

        // 模型配置信息 (基于2025年6月官方文档)
        const modelConfigs = {
            // 最新稳定版 (2025年6月发布)
            'gemini-2.5-pro': {
                name: 'Gemini 2.5 Pro',
                description: '最强大的稳定版模型，具备自适应思维能力',
                status: '最新稳定版',
                contextWindow: '2M tokens',
                costLevel: '高',
                speedLevel: '中等',
                accuracyLevel: '极高',
                features: ['思维模式', '多模态', '函数调用', '代码执行']
            },
            'gemini-2.5-flash': {
                name: 'Gemini 2.5 Flash',
                description: '首个稳定的2.5 Flash模型，性价比最佳',
                status: '最新稳定版',
                contextWindow: '1M tokens',
                costLevel: '中等',
                speedLevel: '高',
                accuracyLevel: '高',
                features: ['快速响应', '多模态', '思维能力']
            },

            // 稳定版模型
            'gemini-2.0-flash': {
                name: 'Gemini 2.0 Flash',
                description: '下一代功能，比1.5 Pro快2倍',
                status: '稳定版',
                contextWindow: '1M tokens',
                costLevel: '中等',
                speedLevel: '极高',
                accuracyLevel: '高',
                features: ['多模态生成', '工具使用', '函数调用']
            },
            'gemini-2.0-flash-lite': {
                name: 'Gemini 2.0 Flash Lite',
                description: '成本优化版本，速度和规模优化',
                status: '稳定版',
                contextWindow: '1M tokens',
                costLevel: '低',
                speedLevel: '极高',
                accuracyLevel: '中等',
                features: ['成本效益', '高速处理']
            },
            'gemini-1.5-pro': {
                name: 'Gemini 1.5 Pro',
                description: '复杂推理和分析的专业版本',
                status: '稳定版',
                contextWindow: '2M tokens',
                costLevel: '中等',
                speedLevel: '中等',
                accuracyLevel: '高',
                features: ['长上下文', '复杂推理', '多模态']
            },
            'gemini-1.5-flash': {
                name: 'Gemini 1.5 Flash',
                description: '快速多用途模型，平衡性能和成本',
                status: '稳定版',
                contextWindow: '1M tokens',
                costLevel: '低',
                speedLevel: '高',
                accuracyLevel: '中等',
                features: ['快速响应', '多模态', '函数调用']
            },
            'gemini-1.5-flash-8b': {
                name: 'Gemini 1.5 Flash 8B',
                description: '最小模型，适合高频调用',
                status: '稳定版',
                contextWindow: '1M tokens',
                costLevel: '极低',
                speedLevel: '极高',
                accuracyLevel: '中等',
                features: ['超低成本', '高频优化']
            },

            // 预览版模型
            'gemini-2.5-flash-lite-preview-06-17': {
                name: 'Gemini 2.5 Flash Lite Preview',
                description: '低成本高性能的2.5模型预览版',
                status: '预览版',
                contextWindow: '1M tokens',
                costLevel: '极低',
                speedLevel: '极高',
                accuracyLevel: '中等',
                features: ['成本优化', '预览功能']
            },
            'gemini-2.0-flash-preview-image-generation': {
                name: 'Gemini 2.0 Flash 图像生成',
                description: '图像生成和编辑预览模型',
                status: '预览版',
                contextWindow: '1M tokens',
                costLevel: '中等',
                speedLevel: '中等',
                accuracyLevel: '高',
                features: ['图像生成', '图像编辑']
            },

            // 特殊功能模型
            'gemini-2.0-flash-live-001': {
                name: 'Gemini 2.0 Flash Live',
                description: '实时交互和双向流式处理',
                status: '特殊功能',
                contextWindow: '1M tokens',
                costLevel: '中等',
                speedLevel: '实时',
                accuracyLevel: '高',
                features: ['实时交互', '语音输出', '流式处理']
            },
            'text-embedding-004': {
                name: 'Text Embedding 004',
                description: '文本嵌入模型，支持弹性嵌入大小',
                status: '稳定版',
                contextWindow: 'N/A',
                costLevel: '低',
                speedLevel: '高',
                accuracyLevel: '高',
                features: ['文本嵌入', '弹性大小', '语义检索']
            },

            // 固定版本
            'gemini-2.0-flash-001': {
                name: 'Gemini 2.0 Flash 001',
                description: '2.0 Flash的固定版本',
                status: '固定版本',
                contextWindow: '1M tokens',
                costLevel: '中等',
                speedLevel: '极高',
                accuracyLevel: '高',
                features: ['版本固定', '文本输出']
            },
            'gemini-1.5-flash-002': {
                name: 'Gemini 1.5 Flash 002',
                description: '1.5 Flash的最新固定版本',
                status: '固定版本',
                contextWindow: '1M tokens',
                costLevel: '低',
                speedLevel: '高',
                accuracyLevel: '中等',
                features: ['版本固定', '稳定性保证']
            },
            'gemini-1.5-pro-002': {
                name: 'Gemini 1.5 Pro 002',
                description: '1.5 Pro的最新固定版本',
                status: '固定版本',
                contextWindow: '2M tokens',
                costLevel: '中等',
                speedLevel: '中等',
                accuracyLevel: '高',
                features: ['版本固定', '复杂推理']
            }
        };

        // 测试场景配置
        const testScenarios = {
            'order-parsing': {
                name: '订单信息解析',
                prompt: `请解析以下订单信息并以JSON格式返回：
订单号：ORD-2024-001
客户：张三公司
项目：网站开发服务 x2 单价：5000元
项目：移动端开发 x1 单价：8000元
总金额：18000元
请返回包含订单号、客户名称、项目列表、总金额的JSON数据。`,
                expectedFields: ['orderNumber', 'customer', 'items', 'totalAmount']
            },
            'multi-format': {
                name: '多文档格式识别',
                prompt: `识别以下文档类型并提取关键信息：
发票抬头：北京科技有限公司
发票号码：INV-2024-0156
开票日期：2024-01-15
商品明细：
- 软件开发服务 数量:1 单价:15000.00 金额:15000.00
- 技术支持服务 数量:12 单价:800.00 金额:9600.00
税率：13%
合计金额：24600.00
请识别文档类型并返回结构化数据。`,
                expectedFields: ['documentType', 'invoiceNumber', 'company', 'items', 'taxRate', 'totalAmount']
            },
            'ocr-extraction': {
                name: '图片OCR文字提取',
                prompt: `模拟OCR场景：从以下模糊文本中提取准确信息：
收据编号：RCP-2O24-OO89 (注意：包含字母O和数字0的混淆)
日期：2O24/O1/2O (注意：包含字母O和数字0的混淆)
金额：￥1,5OO.OO (注意：包含字母O和数字0的混淆)
请纠正OCR错误并返回准确的结构化数据。`,
                expectedFields: ['receiptNumber', 'date', 'amount']
            },
            'data-validation': {
                name: '数据验证与纠错',
                prompt: `验证并纠正以下数据中的错误：
订单信息：
- 订单号：ORD-2024-001
- 日期：2024-13-45 (错误日期)
- 金额：-500.00 (负数金额)
- 数量：0.5个 (小数数量但单位不支持)
- 税率：150% (超出合理范围)
请识别错误并提供合理的纠正建议。`,
                expectedFields: ['errors', 'corrections', 'validatedData']
            },
            'batch-processing': {
                name: '批量数据处理',
                prompt: `批量处理以下3个订单数据：
订单1：客户A，商品X x2，单价100元
订单2：客户B，商品Y x1，单价200元，商品Z x3，单价50元
订单3：客户C，商品X x1，单价100元，商品W x2，单价75元
请计算每个订单的总金额，并生成汇总报告。`,
                expectedFields: ['orders', 'summary', 'totalRevenue']
            }
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadApiKey();
            updateHistoryDisplay();

            // 绑定场景选择事件
            document.querySelectorAll('.scenario-item').forEach(item => {
                item.addEventListener('click', function() {
                    const checkbox = this.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                    this.classList.toggle('selected', checkbox.checked);
                });
            });

            // 初始化选中状态
            document.querySelectorAll('.scenario-item input[type="checkbox"]:checked').forEach(checkbox => {
                checkbox.closest('.scenario-item').classList.add('selected');
            });

            // 绑定模型选择事件
            const modelSelect = document.getElementById('model-select');
            modelSelect.addEventListener('change', updateModelInfo);

            // 初始化模型信息显示
            updateModelInfo();
        });

        // 更新模型信息显示
        function updateModelInfo() {
            const modelSelect = document.getElementById('model-select');
            const selectedModels = Array.from(modelSelect.selectedOptions).map(option => option.value);
            const modelInfo = document.getElementById('model-info');
            const modelDetails = document.getElementById('model-details');

            if (selectedModels.length === 0) {
                modelInfo.style.display = 'none';
                return;
            }

            modelInfo.style.display = 'block';

            if (selectedModels.length === 1) {
                const model = selectedModels[0];
                const config = modelConfigs[model];
                if (config) {
                    modelDetails.innerHTML = `
                        <div><strong>${config.name}</strong> (${config.status})</div>
                        <div style="margin: 5px 0;">${config.description}</div>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 8px;">
                            <span>📊 上下文: ${config.contextWindow}</span>
                            <span>💰 成本: ${config.costLevel}</span>
                            <span>⚡ 速度: ${config.speedLevel}</span>
                            <span>🎯 准确率: ${config.accuracyLevel}</span>
                        </div>
                    `;
                } else {
                    modelDetails.innerHTML = `<div>模型: ${model}</div>`;
                }
            } else {
                const statusCounts = {};
                selectedModels.forEach(model => {
                    const config = modelConfigs[model];
                    if (config) {
                        statusCounts[config.status] = (statusCounts[config.status] || 0) + 1;
                    }
                });

                const statusSummary = Object.entries(statusCounts)
                    .map(([status, count]) => `${status}: ${count}个`)
                    .join(', ');

                modelDetails.innerHTML = `
                    <div><strong>已选择 ${selectedModels.length} 个模型</strong></div>
                    <div style="margin: 5px 0;">状态分布: ${statusSummary}</div>
                    <div style="font-size: 11px; color: #9ca3af; margin-top: 5px;">
                        将进行 ${selectedModels.length} 个模型的对比测试
                    </div>
                `;
            }
        }

        // 初始化图表
        function initializeCharts() {
            // 概览图表
            const overviewCtx = document.getElementById('overview-chart').getContext('2d');
            charts.overview = new Chart(overviewCtx, {
                type: 'radar',
                data: {
                    labels: ['响应速度', '准确率', '稳定性', '成本效益', '功能完整性'],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // 性能对比图表
            const performanceCtx = document.getElementById('performance-chart').getContext('2d');
            charts.performance = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '平均响应时间 (ms)',
                        data: [],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 准确率图表
            const accuracyCtx = document.getElementById('accuracy-chart').getContext('2d');
            charts.accuracy = new Chart(accuracyCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // API Key 管理
        function loadApiKey() {
            const savedKey = localStorage.getItem('gemini-api-key');
            if (savedKey) {
                document.getElementById('api-key').value = savedKey;
            }
        }

        function saveApiKey() {
            const apiKey = document.getElementById('api-key').value;
            if (apiKey) {
                localStorage.setItem('gemini-api-key', apiKey);
            }
        }

        // 开始测试
        async function startTest() {
            const apiKey = document.getElementById('api-key').value;
            if (!apiKey) {
                alert('请先输入 Gemini API Key');
                return;
            }

            saveApiKey();

            const selectedModels = Array.from(document.getElementById('model-select').selectedOptions)
                .map(option => option.value);

            if (selectedModels.length === 0) {
                alert('请至少选择一个模型进行测试');
                return;
            }

            const selectedScenarios = Array.from(document.querySelectorAll('.scenario-item input[type="checkbox"]:checked'))
                .map(checkbox => checkbox.closest('.scenario-item').dataset.scenario);

            if (selectedScenarios.length === 0) {
                alert('请至少选择一个测试场景');
                return;
            }

            const testRounds = parseInt(document.getElementById('test-rounds').value) || 3;

            // 重置测试结果
            testResults = [];

            // 更新UI状态
            document.getElementById('start-test').disabled = true;
            document.getElementById('stop-test').disabled = false;
            document.getElementById('test-status').className = 'status-indicator status-running';
            document.getElementById('progress-container').style.display = 'block';

            // 开始测试
            currentTest = {
                models: selectedModels,
                scenarios: selectedScenarios,
                rounds: testRounds,
                startTime: Date.now(),
                cancelled: false
            };

            logMessage('开始测试...');
            logMessage(`选择的模型: ${selectedModels.join(', ')}`);
            logMessage(`测试场景: ${selectedScenarios.map(s => testScenarios[s]?.name || s).join(', ')}`);
            logMessage(`测试轮数: ${testRounds}`);

            await runTests();
        }

        // 停止测试
        function stopTest() {
            if (currentTest) {
                currentTest.cancelled = true;
                logMessage('用户取消测试');
            }

            resetTestUI();
        }

        // 重置测试UI
        function resetTestUI() {
            document.getElementById('start-test').disabled = false;
            document.getElementById('stop-test').disabled = true;
            document.getElementById('test-status').className = 'status-indicator status-idle';
            document.getElementById('progress-container').style.display = 'none';
            document.getElementById('current-model').textContent = '未开始';
        }

        // 运行测试
        async function runTests() {
            const totalTests = currentTest.models.length * currentTest.scenarios.length * currentTest.rounds;
            let completedTests = 0;

            for (const model of currentTest.models) {
                if (currentTest.cancelled) break;

                for (const scenario of currentTest.scenarios) {
                    if (currentTest.cancelled) break;

                    for (let round = 1; round <= currentTest.rounds; round++) {
                        if (currentTest.cancelled) break;

                        document.getElementById('current-model').textContent = model;
                        updateProgress(completedTests, totalTests, `测试 ${model} - ${testScenarios[scenario]?.name || scenario} (第${round}轮)`);

                        const result = await runSingleTest(model, scenario, round);
                        testResults.push(result);

                        completedTests++;
                        updateRealTimeMetrics();

                        // 短暂延迟避免API限制
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }

            if (!currentTest.cancelled) {
                logMessage('所有测试完成！');
                document.getElementById('test-status').className = 'status-indicator status-success';

                // 保存测试历史
                saveTestHistory();

                // 更新结果显示
                updateResultsDisplay();
            }

            resetTestUI();
        }

        // 运行单个测试
        async function runSingleTest(model, scenario, round) {
            const startTime = Date.now();
            const scenarioConfig = testScenarios[scenario];

            if (!scenarioConfig) {
                throw new Error(`未找到测试场景: ${scenario}`);
            }

            logMessage(`开始测试: ${model} - ${scenarioConfig.name} (第${round}轮)`);

            try {
                const response = await callGeminiAPI(model, scenarioConfig.prompt);
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                // 评估准确率
                const accuracy = evaluateAccuracy(response, scenarioConfig.expectedFields);

                const result = {
                    model,
                    scenario,
                    round,
                    responseTime,
                    accuracy,
                    success: true,
                    response: response.substring(0, 200) + '...', // 截取前200字符
                    timestamp: new Date().toISOString()
                };

                logMessage(`✅ 完成: ${model} - ${responseTime}ms - 准确率: ${accuracy}%`);
                return result;

            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                logMessage(`❌ 错误: ${model} - ${error.message}`);

                return {
                    model,
                    scenario,
                    round,
                    responseTime,
                    accuracy: 0,
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        }

        // 调用 Gemini API
        async function callGeminiAPI(model, prompt) {
            const apiKey = document.getElementById('api-key').value;
            const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

            // 根据模型类型调整配置
            const config = modelConfigs[model] || {};
            const isThinkingModel = model.includes('thinking');

            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 1,
                    topP: 1,
                    maxOutputTokens: isThinkingModel ? 8192 : 2048,
                },
                safetySettings: [
                    {
                        category: "HARM_CATEGORY_HARASSMENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_HATE_SPEECH",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    }
                ]
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`;

                // 提供更详细的错误信息
                if (response.status === 400) {
                    throw new Error(`请求格式错误: ${errorMessage}`);
                } else if (response.status === 401) {
                    throw new Error(`API Key无效或已过期: ${errorMessage}`);
                } else if (response.status === 403) {
                    throw new Error(`权限不足或配额不够: ${errorMessage}`);
                } else if (response.status === 429) {
                    throw new Error(`请求频率过高，请稍后重试: ${errorMessage}`);
                } else if (response.status === 404) {
                    throw new Error(`模型不存在或不可用: ${model}`);
                } else {
                    throw new Error(`API调用失败: ${errorMessage}`);
                }
            }

            const data = await response.json();

            // 检查响应格式
            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('API返回空结果，可能是内容被安全过滤器阻止');
            }

            const candidate = data.candidates[0];

            // 检查是否被安全过滤器阻止
            if (candidate.finishReason === 'SAFETY') {
                throw new Error('内容被安全过滤器阻止');
            }

            if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                throw new Error('API返回格式异常，无法获取生成内容');
            }

            return candidate.content.parts[0].text;
        }

        // 评估准确率
        function evaluateAccuracy(response, expectedFields) {
            try {
                // 尝试解析JSON响应
                let parsedResponse;
                try {
                    // 提取JSON部分（可能包含在markdown代码块中）
                    const jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
                                     response.match(/(\{[\s\S]*\})/);

                    if (jsonMatch) {
                        parsedResponse = JSON.parse(jsonMatch[1]);
                    } else {
                        // 如果没有找到JSON，尝试直接解析整个响应
                        parsedResponse = JSON.parse(response);
                    }
                } catch (e) {
                    // 如果无法解析JSON，基于文本内容评估
                    return evaluateTextAccuracy(response, expectedFields);
                }

                // 检查期望字段的存在
                let foundFields = 0;
                for (const field of expectedFields) {
                    if (hasField(parsedResponse, field)) {
                        foundFields++;
                    }
                }

                return Math.round((foundFields / expectedFields.length) * 100);

            } catch (error) {
                console.error('Accuracy evaluation error:', error);
                return 0;
            }
        }

        // 检查对象是否包含字段（支持嵌套）
        function hasField(obj, field) {
            if (typeof obj !== 'object' || obj === null) return false;

            // 直接检查
            if (obj.hasOwnProperty(field)) return true;

            // 递归检查嵌套对象
            for (const key in obj) {
                if (typeof obj[key] === 'object' && hasField(obj[key], field)) {
                    return true;
                }
            }

            return false;
        }

        // 基于文本内容评估准确率
        function evaluateTextAccuracy(text, expectedFields) {
            let score = 0;
            const lowerText = text.toLowerCase();

            for (const field of expectedFields) {
                // 将驼峰命名转换为可能的文本形式
                const fieldVariations = [
                    field.toLowerCase(),
                    field.replace(/([A-Z])/g, ' $1').toLowerCase(),
                    field.replace(/([A-Z])/g, '_$1').toLowerCase()
                ];

                if (fieldVariations.some(variation => lowerText.includes(variation))) {
                    score++;
                }
            }

            return Math.round((score / expectedFields.length) * 100);
        }

        // 更新进度
        function updateProgress(completed, total, message) {
            const percentage = Math.round((completed / total) * 100);
            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = message;
        }

        // 更新实时指标
        function updateRealTimeMetrics() {
            const successfulTests = testResults.filter(r => r.success);
            const totalTests = testResults.length;

            if (totalTests === 0) return;

            // 平均响应时间
            const avgResponseTime = Math.round(
                successfulTests.reduce((sum, r) => sum + r.responseTime, 0) / successfulTests.length
            );
            document.getElementById('avg-response-time').innerHTML =
                `${avgResponseTime}<span class="metric-unit">ms</span>`;

            // 成功率
            const successRate = Math.round((successfulTests.length / totalTests) * 100);
            document.getElementById('success-rate').innerHTML =
                `${successRate}<span class="metric-unit">%</span>`;

            // 已完成测试数
            document.getElementById('completed-tests').innerHTML =
                `${totalTests}<span class="metric-unit">次</span>`;
        }

        // 记录日志
        function logMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.results-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.results-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-content').classList.add('active');

            // 如果切换到性能或准确率标签，更新图表
            if (tabName === 'performance' || tabName === 'accuracy') {
                setTimeout(() => updateCharts(), 100);
            }
        }

        // 更新结果显示
        function updateResultsDisplay() {
            updateOverviewMetrics();
            updatePerformanceTable();
            updateCharts();
        }

        // 更新概览指标
        function updateOverviewMetrics() {
            if (testResults.length === 0) return;

            const successfulTests = testResults.filter(r => r.success);

            // 按模型分组计算平均响应时间
            const modelPerformance = {};
            successfulTests.forEach(result => {
                if (!modelPerformance[result.model]) {
                    modelPerformance[result.model] = { times: [], accuracies: [] };
                }
                modelPerformance[result.model].times.push(result.responseTime);
                modelPerformance[result.model].accuracies.push(result.accuracy);
            });

            // 找出最快模型
            let fastestModel = '';
            let fastestTime = Infinity;
            for (const [model, data] of Object.entries(modelPerformance)) {
                const avgTime = data.times.reduce((a, b) => a + b, 0) / data.times.length;
                if (avgTime < fastestTime) {
                    fastestTime = avgTime;
                    fastestModel = model;
                }
            }

            // 找出最准确模型
            let mostAccurateModel = '';
            let highestAccuracy = 0;
            for (const [model, data] of Object.entries(modelPerformance)) {
                const avgAccuracy = data.accuracies.reduce((a, b) => a + b, 0) / data.accuracies.length;
                if (avgAccuracy > highestAccuracy) {
                    highestAccuracy = avgAccuracy;
                    mostAccurateModel = model;
                }
            }

            // 推荐模型（综合考虑速度和准确率）
            let recommendedModel = '';
            let bestScore = 0;
            for (const [model, data] of Object.entries(modelPerformance)) {
                const avgTime = data.times.reduce((a, b) => a + b, 0) / data.times.length;
                const avgAccuracy = data.accuracies.reduce((a, b) => a + b, 0) / data.accuracies.length;
                // 综合评分：准确率权重70%，速度权重30%（速度越快分数越高）
                const speedScore = Math.max(0, 100 - (avgTime / 100)); // 假设10秒为满分
                const score = avgAccuracy * 0.7 + speedScore * 0.3;
                if (score > bestScore) {
                    bestScore = score;
                    recommendedModel = model;
                }
            }

            // 总测试时间
            const totalTime = Math.round((Date.now() - currentTest.startTime) / 1000);

            // 更新显示
            document.getElementById('fastest-model').textContent = fastestModel || '-';
            document.getElementById('most-accurate-model').textContent = mostAccurateModel || '-';
            document.getElementById('recommended-model').textContent = recommendedModel || '-';
            document.getElementById('total-test-time').innerHTML =
                `${totalTime}<span class="metric-unit">秒</span>`;
        }

        // 更新性能表格
        function updatePerformanceTable() {
            const tbody = document.getElementById('performance-table-body');
            tbody.innerHTML = '';

            if (testResults.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; color: #6b7280; padding: 20px;">
                            暂无测试数据，请先运行测试
                        </td>
                    </tr>
                `;
                return;
            }

            // 按模型分组统计
            const modelStats = {};
            testResults.forEach(result => {
                if (!modelStats[result.model]) {
                    modelStats[result.model] = { times: [], successes: 0, total: 0 };
                }
                modelStats[result.model].total++;
                if (result.success) {
                    modelStats[result.model].times.push(result.responseTime);
                    modelStats[result.model].successes++;
                }
            });

            // 生成表格行
            for (const [model, stats] of Object.entries(modelStats)) {
                if (stats.times.length === 0) continue;

                const avgTime = Math.round(stats.times.reduce((a, b) => a + b, 0) / stats.times.length);
                const minTime = Math.min(...stats.times);
                const maxTime = Math.max(...stats.times);
                const stability = Math.round((stats.successes / stats.total) * 100);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${model}</td>
                    <td>${avgTime} ms</td>
                    <td>${minTime} ms</td>
                    <td>${maxTime} ms</td>
                    <td>${stability}%</td>
                `;
                tbody.appendChild(row);
            }
        }

        // 更新图表
        function updateCharts() {
            updatePerformanceChart();
            updateAccuracyChart();
            updateOverviewChart();
        }

        // 更新性能图表
        function updatePerformanceChart() {
            const modelStats = {};
            testResults.filter(r => r.success).forEach(result => {
                if (!modelStats[result.model]) {
                    modelStats[result.model] = [];
                }
                modelStats[result.model].push(result.responseTime);
            });

            const labels = Object.keys(modelStats);
            const data = labels.map(model => {
                const times = modelStats[model];
                return Math.round(times.reduce((a, b) => a + b, 0) / times.length);
            });

            charts.performance.data.labels = labels;
            charts.performance.data.datasets[0].data = data;
            charts.performance.update();
        }

        // 更新准确率图表
        function updateAccuracyChart() {
            const modelStats = {};
            testResults.forEach(result => {
                if (!modelStats[result.model]) {
                    modelStats[result.model] = [];
                }
                modelStats[result.model].push(result.accuracy);
            });

            const labels = Object.keys(modelStats);
            const datasets = labels.map((model, index) => ({
                label: model,
                data: modelStats[model],
                borderColor: `hsl(${index * 60}, 70%, 50%)`,
                backgroundColor: `hsla(${index * 60}, 70%, 50%, 0.1)`,
                fill: false
            }));

            charts.accuracy.data.labels = Array.from({length: Math.max(...Object.values(modelStats).map(arr => arr.length))}, (_, i) => `测试 ${i + 1}`);
            charts.accuracy.data.datasets = datasets;
            charts.accuracy.update();
        }

        // 更新概览雷达图
        function updateOverviewChart() {
            const modelStats = {};
            testResults.forEach(result => {
                if (!modelStats[result.model]) {
                    modelStats[result.model] = { times: [], accuracies: [], successes: 0, total: 0 };
                }
                modelStats[result.model].total++;
                if (result.success) {
                    modelStats[result.model].times.push(result.responseTime);
                    modelStats[result.model].accuracies.push(result.accuracy);
                    modelStats[result.model].successes++;
                }
            });

            const datasets = Object.keys(modelStats).map((model, index) => {
                const stats = modelStats[model];
                if (stats.times.length === 0) return null;

                const avgTime = stats.times.reduce((a, b) => a + b, 0) / stats.times.length;
                const avgAccuracy = stats.accuracies.reduce((a, b) => a + b, 0) / stats.accuracies.length;
                const stability = (stats.successes / stats.total) * 100;
                const speedScore = Math.max(0, 100 - (avgTime / 100));
                const costEfficiency = 85; // 模拟值
                const functionality = 90; // 模拟值

                return {
                    label: model,
                    data: [speedScore, avgAccuracy, stability, costEfficiency, functionality],
                    borderColor: `hsl(${index * 60}, 70%, 50%)`,
                    backgroundColor: `hsla(${index * 60}, 70%, 50%, 0.2)`,
                    pointBackgroundColor: `hsl(${index * 60}, 70%, 50%)`
                };
            }).filter(dataset => dataset !== null);

            charts.overview.data.datasets = datasets;
            charts.overview.update();
        }

        // 保存测试历史
        function saveTestHistory() {
            const historyItem = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                models: currentTest.models,
                scenarios: currentTest.scenarios,
                rounds: currentTest.rounds,
                results: testResults,
                summary: {
                    totalTests: testResults.length,
                    successfulTests: testResults.filter(r => r.success).length,
                    avgResponseTime: Math.round(
                        testResults.filter(r => r.success)
                            .reduce((sum, r) => sum + r.responseTime, 0) /
                        testResults.filter(r => r.success).length
                    ),
                    avgAccuracy: Math.round(
                        testResults.reduce((sum, r) => sum + r.accuracy, 0) / testResults.length
                    )
                }
            };

            testHistory.unshift(historyItem);

            // 只保留最近20次测试记录
            if (testHistory.length > 20) {
                testHistory = testHistory.slice(0, 20);
            }

            localStorage.setItem('gemini-test-history', JSON.stringify(testHistory));
            updateHistoryDisplay();
        }

        // 更新历史记录显示
        function updateHistoryDisplay() {
            const historyContainer = document.getElementById('history-list');

            if (testHistory.length === 0) {
                historyContainer.innerHTML = `
                    <p style="text-align: center; color: #6b7280; padding: 20px;">
                        暂无历史记录
                    </p>
                `;
                return;
            }

            historyContainer.innerHTML = testHistory.map(item => `
                <div style="border: 1px solid #e5e7eb; border-radius: 6px; padding: 15px; margin-bottom: 10px; background: white;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong>${new Date(item.timestamp).toLocaleString()}</strong>
                        <button class="test-btn secondary" onclick="loadHistoryItem(${item.id})"
                                style="padding: 4px 8px; font-size: 12px;">
                            查看详情
                        </button>
                    </div>
                    <div style="font-size: 14px; color: #6b7280;">
                        <div>模型: ${item.models.join(', ')}</div>
                        <div>场景: ${item.scenarios.map(s => testScenarios[s]?.name || s).join(', ')}</div>
                        <div>测试: ${item.summary.successfulTests}/${item.summary.totalTests} 成功</div>
                        <div>平均响应: ${item.summary.avgResponseTime}ms | 平均准确率: ${item.summary.avgAccuracy}%</div>
                    </div>
                </div>
            `).join('');
        }

        // 加载历史记录项
        function loadHistoryItem(id) {
            const item = testHistory.find(h => h.id === id);
            if (!item) return;

            // 加载历史数据到当前结果
            testResults = item.results;
            currentTest = {
                models: item.models,
                scenarios: item.scenarios,
                rounds: item.rounds,
                startTime: new Date(item.timestamp).getTime()
            };

            // 更新显示
            updateResultsDisplay();

            // 切换到概览标签
            switchTab('overview');

            alert('历史记录已加载到结果面板');
        }

        // 清空历史记录
        function clearHistory() {
            if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
                testHistory = [];
                localStorage.removeItem('gemini-test-history');
                updateHistoryDisplay();
            }
        }

        // 数据导出
        function exportData(format) {
            if (testResults.length === 0) {
                alert('没有可导出的数据，请先运行测试');
                return;
            }

            const exportOptions = {
                performance: document.getElementById('export-performance').checked,
                accuracy: document.getElementById('export-accuracy').checked,
                logs: document.getElementById('export-logs').checked,
                charts: document.getElementById('export-charts').checked
            };

            switch (format) {
                case 'json':
                    exportJSON(exportOptions);
                    break;
                case 'csv':
                    exportCSV(exportOptions);
                    break;
                case 'pdf':
                    exportPDF(exportOptions);
                    break;
            }
        }

        // 导出JSON
        function exportJSON(options) {
            const exportData = {
                timestamp: new Date().toISOString(),
                testConfig: {
                    models: currentTest.models,
                    scenarios: currentTest.scenarios,
                    rounds: currentTest.rounds
                }
            };

            if (options.performance || options.accuracy) {
                exportData.results = testResults;
            }

            if (options.logs) {
                exportData.logs = document.getElementById('test-log').textContent;
            }

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            downloadFile(blob, `gemini-test-results-${new Date().toISOString().split('T')[0]}.json`);
        }

        // 导出CSV
        function exportCSV(options) {
            if (!options.performance && !options.accuracy) {
                alert('请至少选择性能数据或准确率数据');
                return;
            }

            const headers = ['模型', '场景', '轮次', '响应时间(ms)', '准确率(%)', '成功', '时间戳'];
            const rows = testResults.map(result => [
                result.model,
                testScenarios[result.scenario]?.name || result.scenario,
                result.round,
                result.responseTime,
                result.accuracy,
                result.success ? '是' : '否',
                result.timestamp
            ]);

            const csvContent = [headers, ...rows]
                .map(row => row.map(cell => `"${cell}"`).join(','))
                .join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            downloadFile(blob, `gemini-test-results-${new Date().toISOString().split('T')[0]}.csv`);
        }

        // 导出PDF（简化版）
        function exportPDF(options) {
            alert('PDF导出功能需要额外的库支持，当前版本暂不支持。请使用JSON或CSV格式导出。');
        }

        // 下载文件
        function downloadFile(blob, filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>