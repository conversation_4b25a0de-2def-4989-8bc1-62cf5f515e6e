# Gemini AI 模型性能测试工具使用说明

## 🎯 工具概述

这是一个专门为发票/收据生成器项目开发的Gemini AI模型性能测试工具，用于对比不同Gemini模型在处理实际业务场景时的表现，帮助选择最适合的AI模型。

## 🌟 主要功能

### 1. 多模型对比测试
- **支持的模型**：
  - Gemini 1.5 Flash (快速响应)
  - Gemini 1.5 Flash 8B (轻量级)
  - Gemini 1.5 Pro (专业版)
  - Gemini 2.0 Flash (实验版)
  - Gemini Exp 1206 (实验版)
  - Gemini 2.0 Flash Thinking (思维版)

### 2. 业务场景测试
- **订单信息解析**：测试AI解析订单文本的能力
- **多文档格式识别**：测试识别不同文档类型的准确性
- **图片OCR文字提取**：测试处理OCR错误和文字纠正
- **数据验证与纠错**：测试数据验证和错误修正能力
- **批量数据处理**：测试处理多个订单的效率

### 3. 性能指标监控
- **响应时间**：平均、最快、最慢响应时间
- **准确率**：基于期望字段的匹配度评估
- **稳定性**：成功率和错误率统计
- **综合评分**：速度和准确率的加权评分

### 4. 数据可视化
- **雷达图**：多维度性能对比
- **柱状图**：响应时间对比
- **折线图**：准确率趋势分析
- **性能表格**：详细数据展示

## 🚀 使用指南

### 第一步：配置API Key
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建或获取您的Gemini API Key
3. 在工具中输入API Key（仅本地存储，安全可靠）

### 第二步：选择测试模型
- 可以选择单个或多个模型进行对比
- 建议选择2-3个模型进行对比测试
- 按住 Ctrl/Cmd 键可多选

### 第三步：选择测试场景
- 根据您的实际需求选择相关场景
- 建议至少选择2个场景进行全面测试
- 每个场景都基于真实的业务需求设计

### 第四步：设置测试参数
- **测试轮数**：建议3-5轮，确保结果稳定性
- **批量测试**：支持自动化批量测试

### 第五步：开始测试
- 点击"开始测试"按钮
- 实时监控测试进度和结果
- 可随时停止测试

## 📊 结果分析

### 概览面板
- **最快模型**：响应时间最短的模型
- **最准确模型**：准确率最高的模型
- **推荐模型**：综合评分最高的模型
- **总测试时间**：完整测试耗时

### 性能对比
- **响应时间图表**：直观对比各模型速度
- **性能表格**：详细的统计数据
- **稳定性评分**：基于成功率计算

### 准确率分析
- **准确率趋势**：多轮测试的准确率变化
- **场景详情**：各场景的具体表现
- **错误分析**：失败案例的详细信息

### 历史记录
- **自动保存**：测试结果自动保存到本地
- **历史对比**：可查看和对比历史测试
- **数据持久化**：浏览器本地存储，数据不丢失

## 📁 数据导出

### 支持格式
- **JSON格式**：完整的测试数据和配置
- **CSV格式**：适合Excel分析的表格数据
- **PDF报告**：专业的测试报告（计划中）

### 导出选项
- **性能数据**：响应时间、稳定性等指标
- **准确率数据**：各场景的准确率统计
- **测试日志**：详细的测试过程记录
- **图表数据**：可视化图表的原始数据

## 🔧 技术特性

### 响应式设计
- **移动端适配**：完美支持手机和平板设备
- **触摸友好**：优化的触摸交互体验
- **自适应布局**：适应各种屏幕尺寸

### 性能优化
- **异步处理**：非阻塞的API调用
- **进度显示**：实时的测试进度反馈
- **错误处理**：完善的错误捕获和提示

### 数据安全
- **本地存储**：API Key和测试数据仅在本地存储
- **隐私保护**：不会上传任何敏感信息到服务器
- **安全传输**：使用HTTPS与Google API通信

## 📈 测试建议

### 模型选择建议
1. **快速原型**：选择 Gemini 1.5 Flash
2. **生产环境**：对比 Flash 和 Pro 版本
3. **实验功能**：尝试 2.0 Flash 和 Thinking 版本
4. **成本考虑**：Flash 8B 适合大量请求场景

### 测试场景建议
1. **基础功能**：订单解析 + 多文档识别
2. **高级功能**：OCR提取 + 数据验证
3. **批量处理**：批量数据处理场景
4. **全面测试**：所有场景组合测试

### 结果解读建议
1. **响应时间**：<2秒为优秀，2-5秒为良好
2. **准确率**：>90%为优秀，80-90%为良好
3. **稳定性**：>95%为优秀，90-95%为良好
4. **综合评分**：考虑业务需求权重

## 🛠️ 故障排除

### 常见问题
1. **API Key错误**：检查Key是否正确，是否有权限
2. **网络超时**：检查网络连接，尝试减少并发请求
3. **模型不可用**：某些实验模型可能暂时不可用
4. **准确率异常**：检查测试场景是否适合当前模型

### 错误代码
- **401 Unauthorized**：API Key无效或过期
- **403 Forbidden**：API配额不足或权限不够
- **429 Too Many Requests**：请求频率过高
- **500 Internal Server Error**：Google服务器错误

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 支持6种Gemini模型对比测试
- ✅ 5个业务场景的专项测试
- ✅ 实时性能监控和数据可视化
- ✅ 完整的历史记录和数据导出
- ✅ 响应式设计和移动端适配

### 计划功能
- 📋 PDF报告导出
- 📋 更多测试场景
- 📋 自定义测试用例
- 📋 团队协作功能
- 📋 API使用统计

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 检查本文档的故障排除部分
2. 确认API Key和网络连接正常
3. 查看浏览器控制台的错误信息
4. 尝试刷新页面或清除浏览器缓存

## 🎉 开始使用

现在您可以：
1. 打开 `gemini-model-tester.html` 文件
2. 输入您的Gemini API Key
3. 选择要测试的模型和场景
4. 开始您的第一次AI模型性能测试！

祝您测试愉快，找到最适合您项目的AI模型！ 🚀
